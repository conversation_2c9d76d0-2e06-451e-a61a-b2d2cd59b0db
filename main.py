#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP批处理工具
使用PyQt5界面和esptool.py进行批量烧录
"""

import sys
import os
import json
import threading
import time
from typing import List, Dict, Any
from pathlib import Path

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QLabel, QPushButton, QCheckBox, QLineEdit, QSpinBox, QProgressBar,
    QTextEdit, QFileDialog, QGroupBox, QGridLayout, QListWidget,
    QListWidgetItem, QMessageBox, QComboBox, QSplitter
)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QIcon

import serial.tools.list_ports
import esptool


class PortScanner(QThread):
    """端口扫描线程"""
    ports_found = pyqtSignal(list)
    
    def run(self):
        """扫描可用串口"""
        ports = []
        for port in serial.tools.list_ports.comports():
            ports.append({
                'device': port.device,
                'description': port.description,
                'hwid': port.hwid
            })
        self.ports_found.emit(ports)


class FlashWorker(QThread):
    """固件烧录工作线程"""
    progress_updated = pyqtSignal(str, int)  # port, progress
    log_updated = pyqtSignal(str, str)  # port, message
    finished = pyqtSignal(str, bool, str)  # port, success, error_message

    def __init__(self, port: str, firmware_files: List[Dict], baud_rate: int, chip_type: str = "auto", manual_reset: bool = False, connect_mode: str = "default-reset"):
        super().__init__()
        self.port = port
        self.firmware_files = firmware_files
        self.baud_rate = baud_rate
        self.chip_type = chip_type
        self.manual_reset = manual_reset
        self.connect_mode = connect_mode
        self.is_cancelled = False

    def validate_address(self, address: str) -> bool:
        """验证地址格式是否正确"""
        try:
            if address.startswith('0x') or address.startswith('0X'):
                int(address, 16)
                return True
            else:
                # 尝试解析为十进制
                int(address)
                return True
        except ValueError:
            return False
    
    def cancel(self):
        """取消烧录"""
        self.is_cancelled = True


    
    def run(self):
        """执行固件烧录"""
        try:
            self.log_updated.emit(self.port, f"开始烧录端口: {self.port}")

            # 验证所有地址格式
            for fw_file in self.firmware_files:
                if not self.validate_address(fw_file['address']):
                    error_msg = f"无效的烧录地址格式: {fw_file['address']}"
                    self.log_updated.emit(self.port, error_msg)
                    self.finished.emit(self.port, False, error_msg)
                    return

            # 检查固件文件是否存在
            for fw_file in self.firmware_files:
                if not os.path.exists(fw_file['path']):
                    error_msg = f"固件文件不存在: {fw_file['path']}"
                    self.log_updated.emit(self.port, error_msg)
                    self.finished.emit(self.port, False, error_msg)
                    return

            # 实际执行esptool命令
            self.progress_updated.emit(self.port, 10)

            try:
                # 使用esptool进行烧录，结合高级和低级API以支持进度显示
                import esptool

                # 版本兼容性处理 - 检测是否支持新版API
                try:
                    # 尝试导入新版API来检测是否可用
                    from esptool.cmds import detect_chip, reset_chip
                    use_new_api = True
                    self.log_updated.emit(self.port, f"使用esptool {esptool.__version__} 新版API")
                except ImportError:
                    # 如果导入失败，说明是旧版本，使用兼容模式
                    use_new_api = False
                    self.log_updated.emit(self.port, f"使用esptool {esptool.__version__} 兼容模式")
                except AttributeError:
                    # 版本属性不存在，回退到兼容模式
                    use_new_api = False
                    self.log_updated.emit(self.port, f"使用esptool 兼容模式（版本检测失败）")

                # 检测芯片并连接 - 使用用户选择的复位模式
                self.log_updated.emit(self.port, f"复位模式: {self.connect_mode}")
                if self.connect_mode == 'no-reset':
                    self.log_updated.emit(self.port, "手动复位模式：连接到已进入下载模式的设备...")
                elif self.connect_mode == 'usb-reset':
                    self.log_updated.emit(self.port, "USB复位模式：使用USB复位进入下载模式...")
                else:  # default-reset
                    self.log_updated.emit(self.port, "自动复位模式：自动进入下载模式...")

                connect_mode = self.connect_mode

                if use_new_api:
                    # 使用新版API (esptool 5.x+) - 按照官方示例的简化调用方式
                    with detect_chip(self.port) as esp:
                        # 手动设置波特率（如果需要）
                        if hasattr(esp, '_port') and esp._port:
                            esp._port.baudrate = self.baud_rate

                        # 根据复位模式进行提示（新版API的复位模式在detect_chip时已处理）
                        if self.connect_mode == 'no-reset':
                            self.log_updated.emit(self.port, "手动复位模式：连接到已进入下载模式的设备")
                        elif self.connect_mode == 'usb-reset':
                            self.log_updated.emit(self.port, "USB复位模式：使用USB复位进入下载模式")
                        else:  # default-reset
                            self.log_updated.emit(self.port, "自动复位模式：自动进入下载模式")

                        esp = self._perform_flash_operations(esp, use_new_api)
                else:
                    # 使用旧版API (esptool 4.x及以下)
                    # 注意：旧版API的detect_chip不支持connect_mode参数
                    esp = esptool.ESPLoader.detect_chip(self.port, self.baud_rate)
                    try:
                        # 对于旧版API，connect_mode需要通过其他方式处理
                        if self.connect_mode == 'no-reset':
                            self.log_updated.emit(self.port, "旧版API：手动复位模式，请确保设备已进入下载模式")
                        elif self.connect_mode == 'usb-reset':
                            self.log_updated.emit(self.port, "旧版API：USB复位模式可能不完全支持")

                        esp = self._perform_flash_operations(esp, use_new_api)
                    finally:
                        # 手动关闭连接
                        if hasattr(esp, '_port') and esp._port:
                            esp._port.close()

            except Exception as e:
                error_msg = self.parse_error_message(str(e))
                self.log_updated.emit(self.port, f"esptool执行失败: {error_msg}")
                self.finished.emit(self.port, False, error_msg)

        except Exception as e:
            error_msg = self.parse_error_message(str(e))
            self.log_updated.emit(self.port, f"烧录失败: {error_msg}")
            self.finished.emit(self.port, False, error_msg)

    def _perform_flash_operations(self, esp, use_new_api=True):
        """执行实际的烧录操作"""
        self.log_updated.emit(self.port, f"已连接到 {esp.get_chip_description()}")

        # 运行stub以提高烧录速度（可选）
        try:
            if use_new_api:
                # esptool 5.x+: 使用run_stub函数
                from esptool.cmds import run_stub
                esp = run_stub(esp)
            else:
                # esptool 4.x及以下: 使用esp对象的run_stub方法
                esp = esp.run_stub()
            self.log_updated.emit(self.port, "已加载高速烧录stub")
        except Exception as e:
            self.log_updated.emit(self.port, f"stub加载失败，使用标准模式: {str(e)}")

        # 检测Flash
        if use_new_api:
            # esptool 5.x+: 需要先attach_flash，然后使用flash_id函数
            from esptool.cmds import attach_flash, flash_id
            attach_flash(esp)
            flash_id(esp)
        else:
            # esptool 4.x及以下: 使用esp对象的flash_id方法
            esp.flash_id()
        self.log_updated.emit(self.port, "Flash检测完成")

        if self.is_cancelled:
            self.log_updated.emit(self.port, "烧录已取消")
            self.finished.emit(self.port, False, "用户取消操作")
            return esp

        # 烧录每个固件文件
        total_files = len(self.firmware_files)
        for i, fw_file in enumerate(self.firmware_files):
            if self.is_cancelled:
                self.log_updated.emit(self.port, "烧录已取消")
                self.finished.emit(self.port, False, "用户取消操作")
                return esp

            self.log_updated.emit(self.port, f"烧录文件 ({i+1}/{total_files}): {fw_file['name']} -> {fw_file['address']}")

            # 读取固件文件
            with open(fw_file['path'], 'rb') as f:
                firmware_data = f.read()

            # 解析地址
            address = int(fw_file['address'], 16) if fw_file['address'].startswith('0x') else int(fw_file['address'])

            # 开始烧录此文件
            esp.flash_begin(len(firmware_data), address)

            # 分块写入并更新进度
            block_size = esp.FLASH_WRITE_SIZE if hasattr(esp, 'FLASH_WRITE_SIZE') else 0x400
            blocks = (len(firmware_data) + block_size - 1) // block_size

            for block_num in range(blocks):
                if self.is_cancelled:
                    self.log_updated.emit(self.port, "烧录已取消")
                    self.finished.emit(self.port, False, "用户取消操作")
                    return esp

                start = block_num * block_size
                end = min(start + block_size, len(firmware_data))
                block_data = firmware_data[start:end]

                # 如果是最后一个块且不足block_size，需要填充
                if len(block_data) < block_size:
                    block_data += b'\xff' * (block_size - len(block_data))

                # 根据官方文档，flash_block第二个参数应该是绝对地址
                # 官方示例：esp.flash_block(block, i + FLASH_ADDRESS)
                write_offset = address + start  # 当前块在Flash中的绝对地址
                esp.flash_block(block_data, write_offset)

                # 计算并更新进度
                file_progress = (block_num + 1) / blocks
                total_progress = (i + file_progress) / total_files
                progress_percent = int(10 + total_progress * 80)  # 10-90%
                self.progress_updated.emit(self.port, progress_percent)

            esp.flash_finish()
            self.log_updated.emit(self.port, f"文件 {fw_file['name']} 烧录完成")

        # 重启设备
        self.progress_updated.emit(self.port, 95)
        self.log_updated.emit(self.port, "烧录完成，正在重启设备...")

        # 使用低级API保持一致性，避免与进度跟踪冲突
        esp.hard_reset()

        self.log_updated.emit(self.port, "烧录完成，设备已重启")
        self.progress_updated.emit(self.port, 100)
        self.finished.emit(self.port, True, "烧录成功")

        return esp

    def parse_error_message(self, error_str: str) -> str:
        """解析错误信息，提供更友好的错误描述"""
        error_lower = error_str.lower()

        if "permission denied" in error_lower or "access is denied" in error_lower:
            return "端口被占用或权限不足，请检查是否有其他程序正在使用该端口"
        elif "no such file or directory" in error_lower:
            return "串口设备不存在，请检查设备连接"
        elif "timeout" in error_lower or "timed out" in error_lower:
            return "连接超时，请检查设备连接和波特率设置"
        elif "invalid head of packet" in error_lower:
            return "通信协议错误，请尝试重新连接设备"
        elif "chip not in download mode" in error_lower:
            return "芯片未进入下载模式，请按住BOOT键并重启设备"
        elif "failed to connect" in error_lower:
            return "连接失败，请检查设备连接和端口设置"
        elif "flash read err" in error_lower:
            return "Flash读取错误，可能是硬件问题"
        elif "flash write err" in error_lower:
            return "Flash写入错误，可能是固件文件损坏或硬件问题"
        else:
            return error_str


class ESP32FlasherMainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.config_file = "config.json"
        self.port_workers = {}  # 存储每个端口的工作线程
        self.firmware_files = []  # 固件文件列表
        self.port_status = {}  # 存储每个端口的状态
        self.total_ports = 0  # 总端口数
        self.completed_ports = 0  # 已完成端口数

        self.init_ui()
        self.load_config()
        self.scan_ports()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("ESP批处理工具")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.scan_button = QPushButton("扫描端口")
        self.scan_button.clicked.connect(self.scan_ports)
        button_layout.addWidget(self.scan_button)
        
        self.start_button = QPushButton("开始烧录")
        self.start_button.clicked.connect(self.start_flashing)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止烧录")
        self.stop_button.clicked.connect(self.stop_flashing)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        button_layout.addStretch()
        main_layout.addLayout(button_layout)
    
    def create_left_panel(self) -> QWidget:
        """创建左侧控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 端口选择组
        port_group = QGroupBox("端口选择")
        port_layout = QVBoxLayout(port_group)
        
        self.port_list = QListWidget()
        port_layout.addWidget(self.port_list)
        
        layout.addWidget(port_group)
        
        # 固件文件组
        firmware_group = QGroupBox("固件文件")
        firmware_layout = QVBoxLayout(firmware_group)
        
        fw_button_layout = QHBoxLayout()
        self.add_firmware_button = QPushButton("添加固件")
        self.add_firmware_button.clicked.connect(self.add_firmware_file)
        fw_button_layout.addWidget(self.add_firmware_button)

        self.edit_address_button = QPushButton("编辑地址")
        self.edit_address_button.clicked.connect(self.edit_firmware_address)
        fw_button_layout.addWidget(self.edit_address_button)

        self.remove_firmware_button = QPushButton("移除固件")
        self.remove_firmware_button.clicked.connect(self.remove_firmware_file)
        fw_button_layout.addWidget(self.remove_firmware_button)

        firmware_layout.addLayout(fw_button_layout)
        
        self.firmware_list = QListWidget()
        firmware_layout.addWidget(self.firmware_list)
        
        layout.addWidget(firmware_group)
        
        # 参数设置组
        params_group = QGroupBox("烧录参数")
        params_layout = QGridLayout(params_group)

        params_layout.addWidget(QLabel("波特率:"), 0, 0)
        self.baud_rate_combo = QComboBox()
        self.baud_rate_combo.addItems(['9600', '57600', '115200', '460800', '921600', '1500000'])
        self.baud_rate_combo.setCurrentText('115200')  # 改为更稳定的默认值
        params_layout.addWidget(self.baud_rate_combo, 0, 1)

        params_layout.addWidget(QLabel("芯片类型:"), 1, 0)
        self.chip_type_combo = QComboBox()
        self.chip_type_combo.addItems([
            'auto (自动检测)',
            'esp32',
            'esp32s2',
            'esp32s3',
            'esp32c3',
            'esp32c6',
            'esp32h2'
        ])
        self.chip_type_combo.setCurrentText('auto (自动检测)')
        params_layout.addWidget(self.chip_type_combo, 1, 1)

        params_layout.addWidget(QLabel("默认起始地址:"), 2, 0)
        self.default_address_combo = QComboBox()
        self.default_address_combo.setEditable(True)
        self.default_address_combo.addItems([
            '0x1000',    # Bootloader
            '0x8000',    # Partition table
            '0xe000',    # Boot app0
            '0x10000',   # Application firmware
            '0x3d0000'   # SPIFFS/LittleFS
        ])
        self.default_address_combo.setCurrentText('0x10000')
        params_layout.addWidget(self.default_address_combo, 2, 1)

        # 添加手动复位选项
        params_layout.addWidget(QLabel("复位模式:"), 3, 0)
        self.reset_mode_combo = QComboBox()
        self.reset_mode_combo.addItems([
            '自动复位 (--before default-reset)',
            '手动复位 (--before no-reset)',
            'USB复位 (--before usb-reset)'
        ])
        self.reset_mode_combo.setCurrentText('手动复位 (--before no-reset)')
        self.reset_mode_combo.setToolTip(
            "手动复位: 需要手动按BOOT+RESET进入下载模式，兼容性最好\n"
            "自动复位: esptool自动控制复位，可能在某些硬件上失败\n"
            "USB复位: 适用于支持USB复位的开发板"
        )
        params_layout.addWidget(self.reset_mode_combo, 3, 1)

        layout.addWidget(params_group)
        
        layout.addStretch()
        return panel
    
    def create_right_panel(self) -> QWidget:
        """创建右侧日志和进度面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 进度显示组
        progress_group = QGroupBox("烧录进度")
        progress_layout = QVBoxLayout(progress_group)

        # 总体进度条
        total_progress_widget = QWidget()
        total_progress_layout = QHBoxLayout(total_progress_widget)
        total_progress_layout.addWidget(QLabel("总体进度:"))
        self.total_progress_bar = QProgressBar()
        self.total_progress_bar.setMinimumWidth(200)
        total_progress_layout.addWidget(self.total_progress_bar)
        self.total_progress_label = QLabel("0/0")
        total_progress_layout.addWidget(self.total_progress_label)
        progress_layout.addWidget(total_progress_widget)

        # 各端口进度
        self.progress_widget = QWidget()
        self.progress_layout = QVBoxLayout(self.progress_widget)
        progress_layout.addWidget(self.progress_widget)

        layout.addWidget(progress_group)
        
        # 日志显示组
        log_group = QGroupBox("烧录日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return panel

    def scan_ports(self):
        """扫描可用端口"""
        self.scan_button.setEnabled(False)
        self.scan_button.setText("扫描中...")

        self.port_scanner = PortScanner()
        self.port_scanner.ports_found.connect(self.on_ports_found)
        self.port_scanner.start()

    def on_ports_found(self, ports):
        """端口扫描完成回调"""
        self.port_list.clear()

        for port_info in ports:
            item = QListWidgetItem()
            checkbox = QCheckBox(f"{port_info['device']} - {port_info['description']}")
            checkbox.setProperty('port_device', port_info['device'])

            self.port_list.addItem(item)
            self.port_list.setItemWidget(item, checkbox)

        self.scan_button.setEnabled(True)
        self.scan_button.setText("扫描端口")
        self.log_message(f"扫描完成，发现 {len(ports)} 个端口")

    def add_firmware_file(self):
        """添加固件文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择固件文件", "", "固件文件 (*.bin *.hex);;所有文件 (*.*)"
        )

        if files:
            # 获取默认地址
            default_address = self.default_address_combo.currentText()

            for file_path in files:
                # 使用默认地址，但允许用户修改
                address, ok = self.get_flash_address(default_address)
                if ok:
                    fw_info = {
                        'path': file_path,
                        'address': address,
                        'name': os.path.basename(file_path)
                    }
                    self.firmware_files.append(fw_info)

                    item_text = f"{fw_info['name']} @ {fw_info['address']}"
                    self.firmware_list.addItem(item_text)

                    # 自动递增地址（如果是十六进制）
                    if address.startswith('0x'):
                        try:
                            addr_int = int(address, 16) + 0x1000  # 递增4KB
                            next_address = f"0x{addr_int:x}"
                            self.default_address_combo.setCurrentText(next_address)
                        except ValueError:
                            pass

    def get_flash_address(self, default_address="0x1000"):
        """获取烧录地址对话框"""
        from PyQt5.QtWidgets import QInputDialog

        while True:
            address, ok = QInputDialog.getText(
                self, "设置烧录地址", "请输入烧录地址 (如: 0x1000):", text=default_address
            )

            if not ok:
                return address, ok

            # 验证地址格式
            if self.validate_address_format(address):
                return address, ok
            else:
                QMessageBox.warning(
                    self, "地址格式错误",
                    "请输入有效的十六进制地址格式 (如: 0x1000) 或十进制数字"
                )

    def validate_address_format(self, address: str) -> bool:
        """验证地址格式是否正确"""
        try:
            if address.startswith('0x') or address.startswith('0X'):
                int(address, 16)
                return True
            else:
                # 尝试解析为十进制
                int(address)
                return True
        except ValueError:
            return False

    def edit_firmware_address(self):
        """编辑选中固件文件的地址"""
        current_row = self.firmware_list.currentRow()
        if current_row >= 0:
            fw_info = self.firmware_files[current_row]
            current_address = fw_info['address']

            new_address, ok = self.get_flash_address(current_address)
            if ok and new_address != current_address:
                # 更新固件信息
                fw_info['address'] = new_address

                # 更新列表显示
                item_text = f"{fw_info['name']} @ {fw_info['address']}"
                self.firmware_list.item(current_row).setText(item_text)
        else:
            QMessageBox.information(self, "提示", "请先选择要编辑的固件文件")

    def remove_firmware_file(self):
        """移除选中的固件文件"""
        current_row = self.firmware_list.currentRow()
        if current_row >= 0:
            self.firmware_list.takeItem(current_row)
            del self.firmware_files[current_row]

    def get_selected_ports(self) -> List[str]:
        """获取选中的端口列表"""
        selected_ports = []
        for i in range(self.port_list.count()):
            item = self.port_list.item(i)
            checkbox = self.port_list.itemWidget(item)
            if checkbox.isChecked():
                port_device = checkbox.property('port_device')
                selected_ports.append(port_device)
        return selected_ports

    def start_flashing(self):
        """开始烧录"""
        selected_ports = self.get_selected_ports()

        if not selected_ports:
            QMessageBox.warning(self, "警告", "请至少选择一个端口")
            return

        if not self.firmware_files:
            QMessageBox.warning(self, "警告", "请至少添加一个固件文件")
            return

        # 验证所有固件文件的地址格式
        for fw_file in self.firmware_files:
            if not self.validate_address_format(fw_file['address']):
                QMessageBox.warning(
                    self, "地址格式错误",
                    f"固件文件 '{fw_file['name']}' 的地址格式无效: {fw_file['address']}\n"
                    "请编辑地址为有效的十六进制格式 (如: 0x1000)"
                )
                return

        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 初始化状态
        self.total_ports = len(selected_ports)
        self.completed_ports = 0
        self.port_status = {}
        for port in selected_ports:
            self.port_status[port] = {"status": "进行中", "progress": 0}

        # 更新总体进度
        self.update_total_progress()

        # 清空之前的进度条
        self.clear_progress_bars()

        # 为每个选中的端口创建进度条
        self.create_progress_bars(selected_ports)

        # 启动烧录线程
        baud_rate = int(self.baud_rate_combo.currentText())

        # 获取芯片类型设置
        chip_type_text = self.chip_type_combo.currentText()
        if chip_type_text.startswith('auto'):
            chip_type = "auto"
        else:
            chip_type = chip_type_text

        # 获取复位模式设置
        reset_mode_text = self.reset_mode_combo.currentText()
        manual_reset = reset_mode_text.startswith('手动复位')

        # 确定connect_mode参数
        if reset_mode_text.startswith('手动复位'):
            connect_mode_for_ui = 'no-reset'
        elif reset_mode_text.startswith('USB复位'):
            connect_mode_for_ui = 'usb-reset'
        else:  # 自动复位
            connect_mode_for_ui = 'default-reset'

        for port in selected_ports:
            # 如果是手动复位模式，为每个端口单独显示提示
            if manual_reset:
                # 在日志中显示手动复位步骤
                self.log_message(f"\n=== 手动复位模式 - {port} ===")
                self.log_message("请按照以下步骤操作：")
                self.log_message("1. 按住ESP32上的BOOT键（或IO0键）")
                self.log_message("2. 按一下RESET键（或EN键）")
                self.log_message("3. 松开RESET键，继续按住BOOT键")
                self.log_message("4. 点击下方对话框的'是'开始烧录")
                self.log_message("5. 烧录开始后可以松开BOOT键")
                self.log_message("=" * 40)

                reply = QMessageBox.question(
                    self, f"手动复位模式 - {port}",
                    f"准备烧录端口: {port}\n\n"
                    "请确认已按照日志区域显示的步骤完成手动复位操作\n\n"
                    "ESP32应该已经进入下载模式\n\n"
                    "是否继续烧录此端口？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                if reply != QMessageBox.Yes:
                    self.log_message(f"跳过端口: {port}")
                    continue

            worker = FlashWorker(port, self.firmware_files, baud_rate, chip_type, manual_reset, connect_mode_for_ui)
            worker.progress_updated.connect(self.update_progress)
            worker.log_updated.connect(self.log_message_with_port)
            worker.finished.connect(self.on_flash_finished)

            self.port_workers[port] = worker
            worker.start()

            # 在手动复位模式下，为每个端口添加延迟，避免同时操作多个设备
            if manual_reset and len(selected_ports) > 1:
                import time
                time.sleep(1)

        self.log_message("开始批量烧录...")

    def stop_flashing(self):
        """停止烧录"""
        for worker in self.port_workers.values():
            worker.cancel()

        self.log_message("正在停止烧录...")

    def clear_progress_bars(self):
        """清空进度条"""
        for i in reversed(range(self.progress_layout.count())):
            child = self.progress_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

    def create_progress_bars(self, ports: List[str]):
        """为选中的端口创建进度条"""
        for port in ports:
            port_widget = QWidget()
            port_layout = QHBoxLayout(port_widget)

            label = QLabel(f"{port}:")
            label.setMinimumWidth(80)
            port_layout.addWidget(label)

            progress_bar = QProgressBar()
            progress_bar.setProperty('port', port)
            progress_bar.setMinimumWidth(200)
            port_layout.addWidget(progress_bar)

            status_label = QLabel("准备中...")
            status_label.setProperty('port', port)
            status_label.setMinimumWidth(100)
            port_layout.addWidget(status_label)

            self.progress_layout.addWidget(port_widget)

    def update_progress(self, port: str, progress: int):
        """更新指定端口的进度"""
        # 更新端口状态
        if port in self.port_status:
            self.port_status[port]["progress"] = progress

        # 更新进度条
        for i in range(self.progress_layout.count()):
            widget = self.progress_layout.itemAt(i).widget()
            if widget:
                progress_bar = widget.findChild(QProgressBar)
                if progress_bar and progress_bar.property('port') == port:
                    progress_bar.setValue(progress)

                    # 更新状态标签
                    status_label = None
                    for child in widget.children():
                        if isinstance(child, QLabel) and child.property('port') == port:
                            status_label = child
                            break

                    if status_label:
                        if progress < 100:
                            status_label.setText(f"烧录中... {progress}%")
                        else:
                            status_label.setText("烧录完成")
                    break

    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

    def log_message_with_port(self, port: str, message: str):
        """添加带端口信息的日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] [{port}] {message}")

    def on_flash_finished(self, port: str, success: bool, error_message: str = ""):
        """烧录完成回调"""
        if port in self.port_workers:
            del self.port_workers[port]

        # 更新端口状态
        if port in self.port_status:
            self.port_status[port]["status"] = "成功" if success else "失败"
            if not success and error_message:
                self.port_status[port]["error"] = error_message

        # 更新状态标签
        for i in range(self.progress_layout.count()):
            widget = self.progress_layout.itemAt(i).widget()
            if widget:
                progress_bar = widget.findChild(QProgressBar)
                if progress_bar and progress_bar.property('port') == port:
                    # 找到状态标签并更新
                    for child in widget.children():
                        if isinstance(child, QLabel) and child.property('port') == port:
                            if success:
                                child.setText("✓ 烧录成功")
                                child.setStyleSheet("color: green; font-weight: bold;")
                            else:
                                child.setText("✗ 烧录失败")
                                child.setStyleSheet("color: red; font-weight: bold;")
                            break
                    break

        self.completed_ports += 1
        self.update_total_progress()

        status = "成功" if success else "失败"
        if success:
            self.log_message_with_port(port, f"烧录{status}")
        else:
            self.log_message_with_port(port, f"烧录{status}: {error_message}")

        # 检查是否所有端口都完成了
        if not self.port_workers:
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)

            # 统计结果
            success_count = sum(1 for status in self.port_status.values() if status["status"] == "成功")
            fail_count = self.total_ports - success_count

            self.log_message(f"批量烧录完成 - 成功: {success_count}, 失败: {fail_count}")

    def update_total_progress(self):
        """更新总体进度"""
        if self.total_ports > 0:
            progress_percent = int((self.completed_ports / self.total_ports) * 100)
            self.total_progress_bar.setValue(progress_percent)
            self.total_progress_label.setText(f"{self.completed_ports}/{self.total_ports}")
        else:
            self.total_progress_bar.setValue(0)
            self.total_progress_label.setText("0/0")

    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 恢复波特率设置
                if 'baud_rate' in config:
                    self.baud_rate_combo.setCurrentText(str(config['baud_rate']))

                # 恢复芯片类型设置
                if 'chip_type' in config:
                    self.chip_type_combo.setCurrentText(config['chip_type'])

                # 恢复默认地址设置
                if 'default_address' in config:
                    self.default_address_combo.setCurrentText(config['default_address'])

                # 恢复固件文件列表
                if 'firmware_files' in config:
                    self.firmware_files = config['firmware_files']
                    for fw_info in self.firmware_files:
                        item_text = f"{fw_info['name']} @ {fw_info['address']}"
                        self.firmware_list.addItem(item_text)

        except Exception as e:
            self.log_message(f"加载配置失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        try:
            config = {
                'baud_rate': int(self.baud_rate_combo.currentText()),
                'chip_type': self.chip_type_combo.currentText(),
                'default_address': self.default_address_combo.currentText(),
                'firmware_files': self.firmware_files
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.save_config()

        # 停止所有工作线程
        for worker in self.port_workers.values():
            worker.cancel()
            worker.wait()

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("ESP批处理工具")

    window = ESP32FlasherMainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
