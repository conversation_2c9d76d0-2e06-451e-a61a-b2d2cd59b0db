#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32固件地址验证工具
用于检查固件文件和烧录地址的兼容性
"""

import os
import sys

def check_firmware_address(file_path, address_str):
    """检查固件文件和地址的兼容性"""
    
    print(f"\n=== 固件地址验证 ===")
    print(f"文件: {os.path.basename(file_path)}")
    print(f"地址: {address_str}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 错误: 文件不存在 - {file_path}")
        return False
    
    # 获取文件大小
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
    
    # 解析地址
    try:
        if address_str.startswith('0x'):
            address = int(address_str, 16)
        else:
            address = int(address_str, 16)
        print(f"地址 (十进制): {address}")
        print(f"地址 (十六进制): 0x{address:X}")
    except ValueError:
        print(f"❌ 错误: 无效的地址格式 - {address_str}")
        return False
    
    # 地址分析
    print(f"\n=== 地址分析 ===")
    
    if address == 0x0000:
        print("📍 地址类型: Bootloader起始地址")
        print("✅ 适用于: 完整固件包含bootloader")
        print("⚠️  注意: 确保固件包含bootloader代码")
        
    elif address == 0x1000:
        print("📍 地址类型: ESP32 Bootloader标准地址")
        print("✅ 适用于: ESP32系列bootloader")
        print("✅ ESP32-S3兼容性: 良好")
        
    elif address == 0x8000:
        print("📍 地址类型: 分区表地址")
        print("✅ 适用于: 分区表文件")
        print("⚠️  注意: 通常用于partition-table.bin")
        
    elif address == 0x10000:
        print("📍 地址类型: 应用程序标准地址")
        print("✅ 适用于: 应用程序固件")
        print("✅ ESP32-S3兼容性: 最佳")
        print("✅ 推荐: 这是最常用的应用程序地址")
        
    elif address < 0x1000:
        print("📍 地址类型: 低地址区域")
        print("⚠️  警告: 可能与bootloader冲突")
        if file_size > 4096:
            print("❌ 风险: 大文件使用低地址可能有问题")
        
    else:
        print(f"📍 地址类型: 自定义地址 (0x{address:X})")
        print("⚠️  注意: 确保不与其他组件冲突")
    
    # ESP32-S3特殊检查
    print(f"\n=== ESP32-S3兼容性检查 ===")
    
    # 检查文件名是否包含ESP32-S3相关信息
    filename_lower = os.path.basename(file_path).lower()
    if 'esp32s3' in filename_lower or 's3' in filename_lower:
        print("✅ 检测到ESP32-S3固件")
    
    # 地址兼容性评分
    compatibility_score = 0
    recommendations = []
    
    if address == 0x10000:
        compatibility_score = 10
        print("✅ 兼容性评分: 10/10 (完美)")
        print("✅ 建议: 保持当前地址设置")
        
    elif address == 0x1000:
        compatibility_score = 8
        print("✅ 兼容性评分: 8/10 (很好)")
        print("💡 建议: 如果是应用程序，考虑使用0x10000")
        
    elif address == 0x0000:
        if file_size > 100000:  # 大于100KB可能是完整固件
            compatibility_score = 7
            print("✅ 兼容性评分: 7/10 (良好)")
            print("💡 建议: 确保固件包含完整的bootloader")
        else:
            compatibility_score = 5
            print("⚠️  兼容性评分: 5/10 (一般)")
            print("💡 建议: 小文件不建议使用0x0000地址")
            
    elif address == 0x0800:  # 您原来使用的地址
        compatibility_score = 3
        print("⚠️  兼容性评分: 3/10 (较差)")
        print("❌ 问题: 0x0800不是ESP32标准地址")
        print("💡 强烈建议: 改为0x10000 (应用程序) 或 0x1000 (bootloader)")
        recommendations.append("将地址从0x0800改为0x10000")
        
    else:
        compatibility_score = 6
        print("⚠️  兼容性评分: 6/10 (需要验证)")
        print("💡 建议: 确认地址是否正确")
    
    # 烧录参数建议
    print(f"\n=== 烧录参数建议 ===")
    
    if compatibility_score >= 8:
        print("📋 推荐配置:")
        print("   波特率: 115200 (标准)")
        print("   复位模式: 自动复位")
        print("   Stub Loader: 启用")
        
    elif compatibility_score >= 6:
        print("📋 推荐配置:")
        print("   波特率: 57600 (稳定)")
        print("   复位模式: 手动复位")
        print("   Stub Loader: 可选")
        
    else:
        print("📋 推荐配置 (保守设置):")
        print("   波特率: 57600 或更低")
        print("   复位模式: 手动复位")
        print("   ☑️ 禁用Stub Loader")
        print("   ☑️ ESP32-S3兼容模式")
    
    # 总结建议
    if recommendations:
        print(f"\n=== 重要建议 ===")
        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec}")
    
    print(f"\n=== 验证完成 ===")
    return compatibility_score >= 6

def main():
    """主函数"""
    print("ESP32固件地址验证工具")
    print("=" * 50)
    
    # 示例检查您的固件
    firmware_examples = [
        ("esp-miner-factory-NerdQAxe++-v1.0.31.bin", "0x0800"),  # 您的原设置
        ("esp-miner-factory-NerdQAxe++-v1.0.31.bin", "0x10000"), # 您的新设置
    ]
    
    for firmware_file, address in firmware_examples:
        print(f"\n{'='*60}")
        if os.path.exists(firmware_file):
            check_firmware_address(firmware_file, address)
        else:
            print(f"示例检查: {firmware_file} @ {address}")
            print("(文件不存在，仅显示地址分析)")
            
            # 模拟检查
            try:
                addr = int(address, 16)
                if address == "0x0800":
                    print("❌ 0x0800地址问题:")
                    print("   - 不是ESP32标准地址")
                    print("   - 可能导致'操作不支持'错误")
                    print("   - 建议改为0x10000")
                elif address == "0x10000":
                    print("✅ 0x10000地址优势:")
                    print("   - ESP32应用程序标准地址")
                    print("   - ESP32-S3完全兼容")
                    print("   - 推荐使用")
            except:
                pass
    
    print(f"\n{'='*60}")
    print("💡 总结: 您将地址从0x0800改为0x10000是正确的选择！")
    print("   这应该能解决'操作不支持'的错误。")

if __name__ == "__main__":
    main()
